import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class IssueSuggestionDialog extends StatefulWidget {
  const IssueSuggestionDialog({Key? key}) : super(key: key);

  @override
  State<IssueSuggestionDialog> createState() => _IssueSuggestionDialogState();
}

class _IssueSuggestionDialogState extends State<IssueSuggestionDialog> {
  //region bloc
  late IssueSuggestionDialogBloc issueSuggestionDialogBloc;
  //endregion

  //region Init
  @override
  void initState() {
    issueSuggestionDialogBloc = IssueSuggestionDialogBloc(context);
    issueSuggestionDialogBloc.init();
    super.initState();
  }
  //endregion



  @override
  Widget build(BuildContext context) {
    return body();
  }


  //region Report and issue dialog
   Widget body(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ///Bug
        InkWell(
          onTap: (){
            issueSuggestionDialogBloc.isReport = true;
            issueSuggestionDialogBloc.onTapGoToSupport();


            // FeedbackBloc.isAnIssue = true;
            // var screen = const FeedbackScreen();
            // var route = MaterialPageRoute(builder: (context) => screen);
            // Navigator.push(context, route);
          },
          child: Container(
            padding: const EdgeInsets.all(10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                    height: 25,
                    width: 25,
                    child: SvgPicture.asset(AppImages.bug)),
                horizontalSizedBox(4),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("Report an issue",style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
                      verticalSizedBox(2),
                      Text("Something in app is broken or doesn’t work as expected",style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),),

                    ],
                  ),
                )
              ],
            ),
          ),
        ),
        ///Suggestion
        InkWell(
          onTap: (){
            issueSuggestionDialogBloc.isReport = false;
            issueSuggestionDialogBloc.onTapGoToSupport();

            // FeedbackBloc.isAnIssue = false;
            // var screen = const FeedbackScreen();
            // var route = MaterialPageRoute(builder: (context) => screen);
            // Navigator.push(context, route);
          },
          child: Container(
            padding: const EdgeInsets.all(10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                    height: 25,
                    width: 25,
                    child: SvgPicture.asset(AppImages.suggestion3,color: AppColors.appBlack,)),
                horizontalSizedBox(5),

                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [

                      Text("Suggest an improvement",style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
                      verticalSizedBox(2),
                      Text("New ideas or desired enhancements for this app",style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),),

                    ],
                  ),
                )
              ],
            ),
          ),
        ),
        // verticalSizedBox(10),
        // ///Checkbox
        // ///un-comment
        // // appRadioCheckBox(text:"Attach the current screen as screenshot ",isRadio: false,isActive:false,isExpand: true,fontSize: 13, onTap: (){
        // // }),
        // verticalSizedBox(10),
        verticalSizedBox(30),
        Row(
          children: [
            Expanded(
              child:
              Text("You can add more attachments (photos/videos/docs) in coming screen",style: AppTextStyle.subTitle(textColor: AppColors.writingBlack1),),

              // child: appText("You can add more attachments (photos/videos/docs) in coming screen",color:AppColors.writingColor3,fontSize:12,
              //     fontFamily: AppConstants.rRegular,
              //     maxLine: 2,
              //     fontWeight: FontWeight.w400
              // ),
            ),
          ],
        ) ,
        verticalSizedBox(10),



        ///Go to support

        InkWell(
          onTap: (){
            issueSuggestionDialogBloc.onTapGoToSupport();

          },
          child: Container(
            alignment: Alignment.center,
            width: double.infinity,
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(100), border: Border.all(color: AppColors.appBlack, width: 1.3)),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Text(
              AppStrings.goToSupport,
              style: AppTextStyle.access0(textColor: AppColors.appBlack),
            ),
          ),
        )


      ],
    );
  }
//endregion

}
