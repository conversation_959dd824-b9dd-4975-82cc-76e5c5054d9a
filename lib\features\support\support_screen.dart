
import 'package:flutter/material.dart';
import 'package:swadesic/features/support/all_feedback/all_feedback.dart';
import 'package:swadesic/features/support/give_feedback/give_feedback.dart';
import 'package:swadesic/features/support/my_tickets/my_tickets_screen.dart';
import 'package:swadesic/features/support/support_bloc.dart';
import 'package:swadesic/features/support/tickets_to_me/tickets_to_me_screen.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Support  screen
class SupportScreen extends StatefulWidget {
  final bool isReport;
  final String? targetStoreReference; // Store reference when accessed from store menu
  final String? targetStoreHandle; // Store handle for display

  const SupportScreen({
    Key? key,
    required this.isReport,
    this.targetStoreReference,
    this.targetStoreHandle,
  }) : super(key: key);

  @override
  SupportScreenState createState() => SupportScreenState();
}
// endregion

class SupportScreenState extends State<SupportScreen> with SingleTickerProviderStateMixin {
  //region Bloc and tab controller
  late SupportBloc supportBloc;
  late TabController supportTabCtrl;

  // Context detection variables
  bool get isAccessedFromStoreMenu => widget.targetStoreReference != null;
  bool get isOwnStore => widget.targetStoreReference == AppConstants.appData.storeReference;
  bool get isUserViewingStore => AppConstants.appData.isUserView! && isAccessedFromStoreMenu;
  bool get isStoreViewingOwnStore => AppConstants.appData.isStoreView! && isOwnStore;
  bool get isStoreViewingOtherStore => AppConstants.appData.isStoreView! && isAccessedFromStoreMenu && !isOwnStore;

  int get tabCount {
    if (isStoreViewingOwnStore) return 3; // Tickets to me, My tickets, Create ticket
    if (isUserViewingStore || isStoreViewingOtherStore) return 2; // Create ticket, My tickets
    return 2; // Default: Create ticket, Past tickets
  }

  //endregion
  //region Init
  @override
  void initState() {
    supportTabCtrl = TabController(length: tabCount, vsync: this, initialIndex: 0);
    supportBloc = SupportBloc(context, supportTabCtrl);
    supportBloc.init();
    super.initState();
  }

  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
          appBar: appBar(),
          backgroundColor: AppColors.appWhite,
          // resizeToAvoidBottomInset: true,
          body: StreamBuilder<bool>(
              stream: supportBloc.supportCtrl.stream,
              builder: (context, snapshot) {
                return body();
              })),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isMembershipVisible: false,
      isCartVisible: false,
      isDefaultMenuVisible: false,
      title: AppStrings.support,

    );
  }

  //endregion

  //region Body
  Widget body() {
    return Column(
      children: [
        giveAndAllTabView(),
        Expanded(child: tabView()),
      ],
    );
  }

// endregion


//region Tab view
Widget giveAndAllTabView(){
  return StreamBuilder<bool>(
    stream: supportBloc.supportCtrl.stream,
    builder: (context, snapshot) {
      return SizedBox(
        height: kToolbarHeight * 0.8,
        child: TabBar(
          controller: supportBloc.supportTabCtrl,
          indicator: const UnderlineTabIndicator(
            borderSide: BorderSide(
              color: AppColors.appBlack,
              width: 2.0,
            ),
          ),
          onTap: (index) {
            supportBloc.onSelectTab(data: index);
          },
          labelPadding: const EdgeInsets.symmetric(horizontal: 16.0),
          isScrollable: true,
          tabs: _buildTabs(),
        ),
      );
    }
  );
}
//endregion

  // Build tabs based on context
  List<Widget> _buildTabs() {
    if (isStoreViewingOwnStore) {
      // Store viewing own store: Tickets to me, My tickets, Create ticket
      return [
        Tab(child: Text("Tickets to Me", style: AppTextStyle.sectionHeading(textColor: supportBloc.supportTabCtrl.index == 0 ? AppColors.appBlack : AppColors.writingBlack1))),
        Tab(child: Text("My Tickets", style: AppTextStyle.sectionHeading(textColor: supportBloc.supportTabCtrl.index == 1 ? AppColors.appBlack : AppColors.writingBlack1))),
        Tab(child: Text(AppStrings.createATicket, style: AppTextStyle.sectionHeading(textColor: supportBloc.supportTabCtrl.index == 2 ? AppColors.appBlack : AppColors.writingBlack1))),
      ];
    } else if (isUserViewingStore || isStoreViewingOtherStore) {
      // User viewing store or store viewing other store: Create ticket, My tickets
      return [
        Tab(child: Text(AppStrings.createATicket, style: AppTextStyle.sectionHeading(textColor: supportBloc.supportTabCtrl.index == 0 ? AppColors.appBlack : AppColors.writingBlack1))),
        Tab(child: Text("My Tickets", style: AppTextStyle.sectionHeading(textColor: supportBloc.supportTabCtrl.index == 1 ? AppColors.appBlack : AppColors.writingBlack1))),
      ];
    } else {
      // Default: Create ticket, Past tickets
      return [
        Tab(child: Text(AppStrings.createATicket, style: AppTextStyle.sectionHeading(textColor: supportBloc.supportTabCtrl.index == 0 ? AppColors.appBlack : AppColors.writingBlack1))),
        Tab(child: Text(AppStrings.pastTickets, style: AppTextStyle.sectionHeading(textColor: supportBloc.supportTabCtrl.index == 1 ? AppColors.appBlack : AppColors.writingBlack1))),
      ];
    }
  }

Widget tabView(){
    return TabBarView(
      controller: supportBloc.supportTabCtrl,
      children: _buildTabViews(),
    );
}

  // Build tab views based on context
  List<Widget> _buildTabViews() {
    String currentEntityReference = AppConstants.appData.isUserView!
        ? AppConstants.appData.userReference!
        : AppConstants.appData.storeReference!;

    if (isStoreViewingOwnStore) {
      // Store viewing own store: Tickets to me, My tickets, Create ticket
      return [
        TicketsToMeScreen(entityReference: currentEntityReference),
        MyTicketsScreen(entityReference: currentEntityReference),
        GiveFeedback(isReport: widget.isReport, targetStoreReference: widget.targetStoreReference, targetStoreHandle: widget.targetStoreHandle),
      ];
    } else if (isUserViewingStore || isStoreViewingOtherStore) {
      // User viewing store or store viewing other store: Create ticket, My tickets
      return [
        GiveFeedback(isReport: widget.isReport, targetStoreReference: widget.targetStoreReference, targetStoreHandle: widget.targetStoreHandle),
        MyTicketsScreen(entityReference: currentEntityReference),
      ];
    } else {
      // Default: Create ticket, Past tickets
      return [
        GiveFeedback(isReport: widget.isReport),
        const AllFeedback(),
      ];
    }
  }

}
