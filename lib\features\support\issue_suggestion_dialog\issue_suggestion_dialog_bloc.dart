import 'package:flutter/material.dart';
import 'package:swadesic/features/support/support_screen.dart';
import 'package:swadesic/util/app_constants.dart';


class IssueSuggestionDialogBloc {
  // region Common Variables
  late BuildContext context;
   bool isReport = true;


  // endregion

  //region Text controller

  //endregion

  //region Controller
  //endregion

  // region | Constructor |
  IssueSuggestionDialogBloc(this.context);

  // endregion

  // region Init
  void init(){

  }
// endregion

  //region On Tap Go to support
  onTapGoToSupport(){

    Navigator.pop(context);
    var screen = SupportScreen(
      isReport: isReport,
      // No target store reference since this is general support
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(AppConstants.currentSelectedTab, route);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);

    return;
    // Navigator.pop(context);
    // //User view
    // AppConstants.userPersistentTabController.jumpToTab(AppConstants.appData.storeReference==null?2:4);
    // //Store view
    // AppConstants.userPersistentTabController.jumpToTab(AppConstants.appData.storeReference==null?2:4);
    // //Refresh bottom navigation
    // AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
  }
//endregion


}
